# -*- coding: utf-8 -*-
"""
项目经理服务 - 管理所有项目经理实例的生命周期
"""

import os
from ..manager.project_manager import ProjectManager

class ProjectManagerService:
    """
    一个单例服务，用于创建、管理和分发 ProjectManager 实例。
    """
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(ProjectManagerService, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    def __init__(self):
        # 使用一个字典来存储和管理所有的项目经理实例
        # key: design_doc_path, value: ProjectManager instance
        self.managers = {}
        # 添加ID到路径的映射，支持通过ID快速查找
        self.id_to_path = {}
        print("✅ ProjectManagerService Singleton instance created.")

    def get_or_create_manager(self, design_doc_path: str):
        """
        根据设计文档目录路径获取或创建一个项目经理实例。
        包含目录验证逻辑和工作区初始化。
        """
        # 验证目录是否存在
        if not os.path.exists(design_doc_path) or not os.path.isdir(design_doc_path):
            raise ValueError(f'目录无效或不存在: {design_doc_path}')

        # 检查目录是否可读
        if not os.access(design_doc_path, os.R_OK):
            raise ValueError(f'目录无读取权限: {design_doc_path}')

        if design_doc_path in self.managers:
            existing_manager = self.managers[design_doc_path]

            # ✅ 新增：检查工作区状态，修复缓存与实际状态不同步问题
            if not (os.path.exists(existing_manager.workspace_path) and os.path.exists(existing_manager.status_file_path)):
                print(f"⚠️ 工作区状态异常，重新初始化: {design_doc_path}")
                existing_manager._initialize_workspace()

            print(f"🔄 Returning existing ProjectManager for: {design_doc_path}")
            return existing_manager
        else:
            print(f"✨ Creating new ProjectManager for: {design_doc_path}")
            new_manager = ProjectManager(design_doc_path)

            # 初始化工作区 - 简化版本
            new_manager._initialize_workspace()

            # 存储manager实例
            self.managers[design_doc_path] = new_manager
            # 建立ID到路径的映射
            self.id_to_path[new_manager.id] = design_doc_path

            return new_manager

    def get_manager_status(self, manager_id: str):
        """
        获取特定项目经理的状态。
        """
        # 通过ID快速查找manager
        if manager_id in self.id_to_path:
            path = self.id_to_path[manager_id]
            manager = self.managers[path]
            return manager.get_status()
        return None
    
    def get_manager_by_id(self, manager_id: str):
        """
        通过ID获取项目经理实例。
        """
        if manager_id in self.id_to_path:
            path = self.id_to_path[manager_id]
            return self.managers[path]
        return None

    def list_all_managers(self):
        """
        列出所有活跃的项目经理实例。
        """
        return list(self.managers.values())

    def list_all_manager_statuses(self):
        """
        列出所有活跃的项目经理实例的状态。
        """
        return [mgr.get_status() for mgr in self.managers.values()]



# 初始化单例
project_manager_service = ProjectManagerService()
