# -*- coding: utf-8 -*-
"""
项目经理V2蓝图 - 处理项目经理相关的Web接口
"""

import os
import json
import asyncio
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_socketio import emit, join_room, leave_room

# 创建蓝图
pm_v2_bp = Blueprint('pm_v2', __name__, url_prefix='/api/pm_v2')

@pm_v2_bp.route('/get_and_create', methods=['POST'])
def get_and_create_manager():
    """
    获取或创建项目经理实例
    """
    from ..app import web_app
    try:
        data = request.get_json()
        if not data or 'design_doc_path' not in data:
            return jsonify({'success': False, 'error': '请求数据为空或缺少 design_doc_path 参数'}), 400

        design_doc_path = data.get('design_doc_path')

        # 调用服务层处理业务逻辑
        if web_app.project_manager_service:
            manager = web_app.project_manager_service.get_or_create_manager(design_doc_path)
            
            print(f"🚀 [Blueprint] 已成功调用服务层为路径创建项目经理: {design_doc_path}")

            return jsonify({
                'success': True,
                'message': '项目经理实例已创建/获取',
                'manager_id': manager.id,
                'design_doc_path': design_doc_path
            })
        else:
            return jsonify({'success': False, 'error': '项目经理服务未初始化'}), 500

    except ValueError as e:
        print(f"❌ [Blueprint] 目录验证失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 400
    except Exception as e:
        print(f"❌ [Blueprint] 获取或创建项目经理时发生错误: {e}")
        return jsonify({'success': False, 'error': f'内部错误: {str(e)}'}), 500

@pm_v2_bp.route('/manager_status/<manager_id>', methods=['GET'])
def get_manager_status(manager_id):
    """
    获取特定项目经理的状态
    """
    try:
        # TODO: 调用服务层来获取状态
        # status = project_manager_service.get_manager_status(manager_id)
        # if status is None:
        #     return jsonify({'success': False, 'error': '项目经理实例不存在'}), 404
        
        # 模拟成功响应
        print(f"📡 [Blueprint] 接收到获取项目经理状态的请求, ID: {manager_id}")
        print(f"下一步: 将调用 ProjectManagerService 来获取状态。")
        
        status = {"state": "pending", "message": "等待服务层实现"}

        return jsonify({'success': True, 'status': status})

    except Exception as e:
        print(f"❌ [Blueprint] 获取项目经理状态时发生错误: {e}")
        return jsonify({'success': False, 'error': f'内部错误: {str(e)}'}), 500


@pm_v2_bp.route('/log_directory_action', methods=['POST'])
def log_directory_action():
    """
    记录目录操作日志
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据为空'
            }), 400

        action_type = data.get('action_type', 'unknown')
        directory_path = data.get('directory_path', '')
        user_input = data.get('user_input', '')
        result = data.get('result', {})

        # 记录日志
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'action_type': action_type,
            'directory_path': directory_path,
            'user_input': user_input,
            'result': result
        }

        print(f"📝 目录操作日志: {log_entry}")

        return jsonify({
            'success': True,
            'message': '日志记录成功',
            'log_entry': log_entry
        })

    except Exception as e:
        print(f"❌ 记录日志时发生错误: {e}")
        return jsonify({
            'success': False,
            'error': f'记录日志时发生内部错误: {str(e)}'
        }), 500

@pm_v2_bp.route('/list_managers', methods=['GET'])
def list_project_managers():
    """
    列出所有活跃的项目经理实例
    """
    try:
        # TODO: 调用服务层来获取所有实例信息
        # managers_info = project_manager_service.list_all_managers()

        # 模拟成功响应
        print(f"📋 [Blueprint] 接收到列出所有项目经理的请求。")
        print(f"下一步: 将调用 ProjectManagerService 来获取列表。")
        
        managers_info = []

        return jsonify({'success': True, 'managers': managers_info})

    except Exception as e:
        print(f"❌ [Blueprint] 列出项目经理时发生错误: {e}")
        return jsonify({'success': False, 'error': f'内部错误: {str(e)}'}), 500


# ===== HTTP API v2 接口 (符合UI架构标准) =====

@pm_v2_bp.route('/v2/workspace/<task_id>', methods=['GET'])
def get_workspace_info(task_id):
    """
    获取工作区信息 - API v2标准
    """
    from ..app import web_app
    try:
        if not web_app.project_manager_service:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'SERVICE_NOT_AVAILABLE',
                    'message': '项目经理服务未初始化'
                }
            }), 500

        # 通过task_id查找对应的项目经理
        manager = web_app.project_manager_service.get_manager_by_id(task_id)
        if not manager:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'WORKSPACE_NOT_FOUND',
                    'message': f'工作区不存在: {task_id}'
                }
            }), 404

        # 获取工作区信息
        workspace_info = {
            'task_id': manager.id,
            'project_path': manager.design_doc_path,
            'workspace_path': manager.workspace_path,
            'status_file_path': manager.status_file_path,
            'created_at': manager.status.created_at if manager.status else None,
            'last_updated': manager.status.last_updated if manager.status else None,
            'project_state': manager.status.state.value if manager.status else 'initialized'
        }

        return jsonify({
            'success': True,
            'data': workspace_info,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ [API v2] 获取工作区信息失败: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '获取工作区信息失败',
                'details': str(e)
            }
        }), 500

@pm_v2_bp.route('/v2/workspace/<task_id>/status', methods=['GET'])
def get_workspace_status(task_id):
    """
    获取工作区状态 - API v2标准
    """
    from ..app import web_app
    try:
        if not web_app.project_manager_service:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'SERVICE_NOT_AVAILABLE',
                    'message': '项目经理服务未初始化'
                }
            }), 500

        # 通过task_id查找对应的项目经理
        manager = web_app.project_manager_service.get_manager_by_id(task_id)
        if not manager:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'WORKSPACE_NOT_FOUND',
                    'message': f'工作区不存在: {task_id}'
                }
            }), 404

        # 获取详细状态信息
        status_info = manager.get_status()
        review_progress = manager.get_review_progress()

        return jsonify({
            'success': True,
            'data': {
                'task_id': manager.id,
                'status': status_info,
                'review_progress': review_progress,
                'workspace_exists': os.path.exists(manager.workspace_path) if manager.workspace_path else False
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ [API v2] 获取工作区状态失败: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '获取工作区状态失败',
                'details': str(e)
            }
        }), 500

@pm_v2_bp.route('/v2/workspace', methods=['POST'])
def create_workspace():
    """
    创建工作区 - API v2标准
    """
    from ..app import web_app
    try:
        data = request.get_json()
        if not data or 'project_path' not in data:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'INVALID_REQUEST',
                    'message': '缺少必需参数: project_path'
                }
            }), 400

        project_path = data.get('project_path')

        if not web_app.project_manager_service:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'SERVICE_NOT_AVAILABLE',
                    'message': '项目经理服务未初始化'
                }
            }), 500

        # 创建或获取项目经理实例
        manager = web_app.project_manager_service.get_or_create_manager(project_path)

        return jsonify({
            'success': True,
            'data': {
                'task_id': manager.id,
                'project_path': manager.design_doc_path,
                'workspace_path': manager.workspace_path,
                'status_file_path': manager.status_file_path,
                'message': '工作区创建成功'
            },
            'timestamp': datetime.now().isoformat()
        })

    except ValueError as e:
        return jsonify({
            'success': False,
            'error': {
                'code': 'INVALID_PROJECT_PATH',
                'message': '项目路径无效',
                'details': str(e)
            }
        }), 400
    except Exception as e:
        print(f"❌ [API v2] 创建工作区失败: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '创建工作区失败',
                'details': str(e)
            }
        }), 500

@pm_v2_bp.route('/v2/workspace/<task_id>/review', methods=['POST'])
def start_review(task_id):
    """
    启动审查任务 - API v2标准
    """
    from ..app import web_app
    try:
        if not web_app.project_manager_service:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'SERVICE_NOT_AVAILABLE',
                    'message': '项目经理服务未初始化'
                }
            }), 500

        # 通过task_id查找对应的项目经理
        manager = web_app.project_manager_service.get_manager_by_id(task_id)
        if not manager:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'WORKSPACE_NOT_FOUND',
                    'message': f'工作区不存在: {task_id}'
                }
            }), 404

        # 启动审查任务
        result = manager.start_review()

        return jsonify({
            'success': True,
            'data': {
                'task_id': manager.id,
                'review_started': True,
                'message': '审查任务启动成功'
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ [API v2] 启动审查任务失败: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '启动审查任务失败',
                'details': str(e)
            }
        }), 500

@pm_v2_bp.route('/v2/workspaces', methods=['GET'])
def list_workspaces():
    """
    列出所有工作区 - API v2标准
    """
    from ..app import web_app
    try:
        if not web_app.project_manager_service:
            return jsonify({
                'success': False,
                'error': {
                    'code': 'SERVICE_NOT_AVAILABLE',
                    'message': '项目经理服务未初始化'
                }
            }), 500

        # 获取所有项目经理实例
        managers = web_app.project_manager_service.list_all_managers()

        workspaces = []
        for manager in managers:
            workspace_info = {
                'task_id': manager.id,
                'project_path': manager.design_doc_path,
                'workspace_path': manager.workspace_path,
                'project_state': manager.status.state.value if manager.status else 'initialized',
                'created_at': manager.status.created_at if manager.status else None,
                'last_updated': manager.status.last_updated if manager.status else None
            }
            workspaces.append(workspace_info)

        return jsonify({
            'success': True,
            'data': {
                'workspaces': workspaces,
                'total_count': len(workspaces)
            },
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ [API v2] 列出工作区失败: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '列出工作区失败',
                'details': str(e)
            }
        }), 500

@pm_v2_bp.route('/constraint_detail', methods=['GET'])
def get_constraint_detail():
    """
    获取约束详情 - 解决前端404错误
    """
    try:
        # 获取查询参数
        constraint_id = request.args.get('constraintId', 'C001')
        project_id = request.args.get('projectId', 'default')
        
        # 模拟约束详情数据
        constraint_data = {
            "id": constraint_id,
            "category": "boundary_condition",
            "type": "response_time",
            "description": "所有面向用户的API必须在1000ms内响应",
            "source": "系统设计规范",
            "status": "ACTIVE",
            "parameters": {
                "max_response_time_ms": 1000,
                "timeout_threshold": 0.8,
                "retry_count": 3
            },
            "lineage": {
                "parent_id": None,
                "forked_from": None,
                "children_count": 2
            },
            "created_at": "2025-01-31T14:17:30Z",
            "updated_at": "2025-01-31T14:17:30Z"
        }

        return jsonify({
            'success': True,
            'data': constraint_data,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        print(f"❌ [API] 获取约束详情失败: {e}")
        return jsonify({
            'success': False,
            'error': {
                'code': 'INTERNAL_ERROR',
                'message': '获取约束详情失败',
                'details': str(e)
            }
        }), 500
